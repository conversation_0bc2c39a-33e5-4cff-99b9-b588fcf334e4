{"name": "time-flow-pro", "private": true, "version": "0.0.1", "description": "Professional time tracking and billing management system", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.0", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@storybook/addon-a11y": "^9.1.1", "@storybook/addon-docs": "^9.1.1", "@storybook/addon-svelte-csf": "^5.0.7", "@storybook/addon-vitest": "^9.1.1", "@storybook/sveltekit": "^9.1.1", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/svelte": "^5.2.8", "@types/node": "^20", "@vitest/browser": "^3.2.3", "@vitest/ui": "^3.2.4", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-storybook": "^9.1.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "playwright": "^1.53.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^9.1.1", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0"}, "dependencies": {"date-fns": "^4.1.0", "uuid": "^11.1.0"}, "lint-staged": {"*.{js,ts,svelte}": ["eslint --fix", "prettier --write"], "*.{json,md,css,html}": ["prettier --write"]}, "engines": {"node": ">=18.0.0"}}