<script lang="ts">
	import './button.css';

	interface Props {
		/** Is this the principal call to action on the page? */
		primary?: boolean;
		/** What background color to use */
		backgroundColor?: string;
		/** How large should the button be? */
		size?: 'small' | 'medium' | 'large';
		/** Button contents */
		label: string;
		/** The onclick event handler */
		onclick?: (() => void) | undefined;
	}

	const { primary = false, backgroundColor, size = 'medium', label, ...props }: Props = $props();

	const mode = $derived(primary ? 'storybook-button--primary' : 'storybook-button--secondary');
	const style = $derived(backgroundColor ? `background-color: ${backgroundColor}` : '');
</script>

<button
	type="button"
	class={['storybook-button', `storybook-button--${size}`, mode].join(' ')}
	{style}
	{...props}
>
	{label}
</button>
