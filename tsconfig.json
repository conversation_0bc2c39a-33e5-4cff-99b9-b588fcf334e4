{
	"extends": "./.svelte-kit/tsconfig.json",
	"compilerOptions": {
		// TypeScript 5.5+ strict mode configuration
		"strict": true,
		"exactOptionalPropertyTypes": true,
		"noUncheckedIndexedAccess": true,
		"noImplicitReturns": true,
		"noFallthroughCasesInSwitch": true,
		"noImplicitOverride": true,

		// Module resolution and compatibility
		"allowJs": true,
		"checkJs": true,
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"resolveJsonModule": true,
		"skipLibCheck": true,
		"moduleResolution": "bundler",

		// Development and debugging
		"sourceMap": true,
		"declaration": true,
		"declarationMap": true,

		// Additional strict checks for professional development
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noImplicitAny": true,
		"strictNullChecks": true,
		"strictFunctionTypes": true,
		"strictBindCallApply": true,
		"strictPropertyInitialization": true,
		"alwaysStrict": true
	}
	// Path aliases are handled by https://svelte.dev/docs/kit/configuration#alias
	// except $lib which is handled by https://svelte.dev/docs/kit/configuration#files
	//
	// If you want to overwrite includes/excludes, make sure to copy over the relevant includes/excludes
	// from the referenced tsconfig.json - TypeScript does not merge them in
}
